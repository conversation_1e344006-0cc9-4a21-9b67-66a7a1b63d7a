<?php

declare(strict_types=1);

namespace tests;

if (!defined('API_PATH_ROOT')) {
    define('API_PATH_ROOT', realpath(__DIR__ . '/../../../'));
}

require_once __DIR__ . '/../Autoloader.php';

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestDox;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Depends;
use tools\deckbuilder\DeckBuilder;
use tools\deckanalyzer\DeckAnalyzer;

class DeckBuilderTest extends TestCase
{
    private DeckBuilder $DeckBuilder;
    private DeckAnalyzer $DeckAnalyzer;
    private array $_allCreatedDecks;
    private array $deckParams;

    protected function setUp(): void
    {
        $this->DeckBuilder = new DeckBuilder();
        $this->DeckAnalyzer = new DeckAnalyzer();

        $jsonPath = __DIR__ . '/createDeckParams.json';
        $json = file_get_contents($jsonPath);
        $this->deckParams = json_decode($json, true);

        $this->_allCreatedDecks = [];
        foreach ($this->deckParams as $level => $testCases) {
            foreach ($testCases as $index => $params) {
                $deck = $this->DeckBuilder->create($level, $params);
                $this->_allCreatedDecks[] = ['deck' => $deck, 'params' => $params, 'level' => $level];
            }
        }
    }

    #[Test]
    #[TestDox('Test that the DeckBuilder can be instantiated')]
    public function testCanBeCreated(): void
    {
        $this->assertInstanceOf(DeckBuilder::class, $this->DeckBuilder);
    }

    #[Test]
    #[TestDox('Test that the DeckBuilder can build a deck with provided parameters')]
    public function testCreateDeck(): void
    {
        foreach ($this->_allCreatedDecks as $createdDeckInfo) {
            $deck = $createdDeckInfo['deck'];
            $params = $createdDeckInfo['params'];

            $this->assertIsArray($deck);
            $this->assertIsArray($deck['decks']);

            $expectedDecks = $params['deckVersions'] ?? 1;
            $createdDecks = array_filter($deck['decks'], fn($d) => !empty($d));
            $this->assertCount($expectedDecks, $createdDecks);

            for ($i = 0; $i < $expectedDecks; $i++) {
                $this->assertIsArray($deck['decks'][$i]);
                if ($i < 2) {
                    $this->assertCount(9, $deck['decks'][$i]);
                    foreach ($deck['decks'][$i] as $card) {
                        $this->assertIsString($card);
                    }
                }
            }
        }
    }

    #[Test]
    #[TestDox('Test that the DeckAnalyzer can analyze real created decks')]
    public function testDeckAnalyzer(): void
    {
        $sumScores = 0;
        $totalDecks = 0;
        $scoreAverage = 0;

        foreach ($this->_allCreatedDecks as $createdDeckInfo) {
            $createdDeckResult = $createdDeckInfo['deck'];
            $params = $createdDeckInfo['params'];
            $level = $createdDeckInfo['level'];

            foreach ($createdDeckResult['decks'] as $deckKey => $deck) {
                // Ensure the deck is not empty before analyzing
                $deck = array_filter($deck, fn($a) => $a);
                if (!empty($deck) && count($deck) == 9) {
                    $result = $this->DeckAnalyzer->analyze($deck, "advanced");
                    $totalDecks++;
                    $sumScores += $result['analysis']['synergy']['score'];

                    $this->assertIsArray($result);
                    $this->assertNotEmpty($result);

                    // validate win condition name in deck
                    if ($params['winConditionName'] != 'null' && $deckKey < 2) {
                        $this->assertContains($params['winConditionName'], $deck, 'Win condition ' . $params['winConditionName'] . ' not found in deck ' . json_encode($deck));
                    }

                    // validate attack score level in deck with min/max scores
                    if (isset($params['attackLevel']) && $params['attackLevel'] != 'free' && isset($params['championName']) && $params['championName'] == 'null' && $deckKey < 2) {
                        $attackLevelRanges = [
                            'normal' => ['min' => 35, 'max' => 60],
                            'aggressive' => ['min' => 61, 'max' => 70],
                            'very-aggressive' => ['min' => 71, 'max' => 100]
                        ];

                        $minScore = $attackLevelRanges[$params['attackLevel']]['min'];
                        $maxScore = $attackLevelRanges[$params['attackLevel']]['max'];

                        $this->assertLessThanOrEqual(
                            $maxScore,
                            $result['analysis']['attack']['score'],
                            'Attack level is too high, expected max: ' . $maxScore . '%, got: ' . $result['analysis']['attack']['score'] . '% with params: ' . json_encode($params) . ' and deck: ' . json_encode($deck)
                        );
                        $this->assertGreaterThanOrEqual(
                            $minScore,
                            $result['analysis']['attack']['score'],
                            'Attack level is too low, expected min: ' . $minScore . '%, got: ' . $result['analysis']['attack']['score'] . '% with params: ' . json_encode($params) . ' and deck: ' . json_encode($deck)
                        );
                    }

                    // validate champion name in deck
                    if (isset($params['championName']) && $params['championName'] != 'null' && $deckKey < 2) {
                        $this->assertContains($params['championName'], $deck, 'Champion ' . $params['championName'] . ' not found in deck ' . json_encode($deck));
                    }

                    // validate ratio averageElixirCost +- 0.75
                    if (($level == 'advanced' || $level == 'intermediate') && isset($params['averageElixir']) && $params['winConditionName'] == 'null' && isset($params['championName']) && $params['championName'] == 'null' && $deckKey < 2) {
                        $ratio = 0.75;
                        $this->assertLessThanOrEqual($params['averageElixir'] + $ratio, $result['data']['averageElixirCost'], 'Average elixir cost is too high, expected: ' . $params['averageElixir'] . ' + ' . $ratio . ', got: ' . $result['data']['averageElixirCost']);
                        $this->assertGreaterThanOrEqual($params['averageElixir'] - $ratio, $result['data']['averageElixirCost'], 'Average elixir cost is too low, expected: ' . $params['averageElixir'] . ' - ' . $ratio . ', got: ' . $result['data']['averageElixirCost']);
                    }
                }
            }
        }

        $scoreAverage = round($sumScores / $totalDecks, 2);
        error_log("Average Synergy Score: " . $scoreAverage);
    }
}
